import 'package:flutter/widgets.dart';
import 'package:gp_feat_ticket/domain/entity/ticket/ticket_list.entity.dart';
import 'package:gp_shared/presentation/base/base_list_view_wrapper.dart';

import 'bloc/ticket_activity_list_bloc.dart';
import 'widget/ticket_activity_listview.dart';

class TicketActivityTabWidget extends StatefulWidget {
  const TicketActivityTabWidget({
    super.key,
    required this.entity,
  });

  final TicketEntity entity;

  @override
  State<TicketActivityTabWidget> createState() => _TicketActivityTabState();
}

class _TicketActivityTabState extends State<TicketActivityTabWidget> {
  final TicketActivityListBloc bloc = TicketActivityListBloc();

  @override
  void initState() {
    super.initState();
    bloc..initInputData(widget.entity.id);
  }

  @override
  Widget build(BuildContext context) {
    return GPBaseListViewWrapperV2(
      params: GPBaseListViewParamsV2(
        listBloc: bloc,
        inputData: widget.entity.id,
        onRefresh: () {},
      ),
      builder: (GPBaseListViewParamsV2 params) {
        return TicketActivityListView(
          params: params,
          shrinkWrap: true,
          ticketId: widget.entity.id.toString(),
        );
      },
    );
  }
}
