import 'package:auto_mappr_annotation/auto_mappr_annotation.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/utils/gp_sentry.dart';
import 'package:gp_feat_ticket/domain/entity/ticket/ticket_node.entity.dart';

import '../../../../../widgets/base/base_ticket_input_behavior.dart';
import '../../../../domain/entity/enums/ticket/ticket_form_field_type.dart';
import '../../../../domain/entity/workflow/workflow_form.entity.dart';
import '../../../details/widgets/details_tab/details_tabs.dart';
import '../../widgets/input/input_behavior.dart';
import '../../widgets/shared/title_widget.dart';
import '../input_dynamic_form_details.dart';
import 'base/base_input_text_params.dart';
import 'base_form_input.dart';

const maxTableRows = 100;

final class TicketCreateInputTableParams extends BaseInputTextParams {
  TicketCreateInputTableParams({
    required super.id,
    required super.formFieldType,
    required this.title,
    required this.isRequired,
    required super.formIndex,
    super.isReadOnly = true,
    this.fieldValues,
    this.hint,
    this.subTitle,
    super.permissions,
  });

  factory TicketCreateInputTableParams.fromWorkFlowFormFieldEntity(
    WorkFlowFormFieldEntity entity, {
    required int formIndex,
    bool isReadOnly = true,
    WorkflowFormFieldPermissionEntity? permissions,
  }) {
    // 1 entity = 1 table có nhiều columns
    final isExpanded = entity.isExpanded ?? true;
    final columns = entity.option.columns;

    final List<TicketTableFieldValues> rowValues = [];

    if (columns != null) {
      int index = 1;
      List<WorkFlowFieldValuesEntity> fieldValues = [];

      for (var i = 0; i < columns.length; i++) {
        final formEntity = columns[i].field;

        final option = formEntity.option;

        fieldValues.add(
          WorkFlowFieldValuesEntity(
            ticketId: -1,
            workspaceId: formEntity.workspaceId.toString(),
            fieldId: formEntity.id,
            value: null,
            // clone object
            info: formEntity.copyWith(
              option: option.copyWith(
                formulaArgs: option.formulaArgs?.toList(),
                currencyPool: option.currencyPool?.toList(),
                selectionPool: option.selectionPool?.toList(),
                selections: option.selections?.toList(),
                columns: option.columns?.toList(),
              ),
            ),
            isExpanded: isExpanded,
          ),
        );
      }

      rowValues.add(
        TicketTableFieldValues(
          index: index,
          fieldValues: fieldValues,
          isExpanded: isExpanded,
        ),
      );
    }

    return TicketCreateInputTableParams(
      id: entity.id,
      formIndex: formIndex,
      formFieldType: entity.type,
      title: entity.title,
      hint: entity.hint,
      isRequired: entity.isRequired,
      subTitle: entity.isRequired ? '*' : '',
      isReadOnly: isReadOnly,
      fieldValues: rowValues,
      permissions: permissions,
    );
  }

  factory TicketCreateInputTableParams.fromWorkFlowFieldValuesEntity(
    WorkFlowFieldValuesEntity entity, {
    required int formIndex,
    bool isReadOnly = true,
    WorkflowFormFieldPermissionEntity? permissions,
  }) {
    final isExpanded = entity.isExpanded ?? true;

    final WorkFlowFormFieldEntity formFieldEntity = entity.info;
    final columns = formFieldEntity.option.columns;

    final List<TicketTableFieldValues> rowValues = [];

    entity.value ??= [];

    if (entity.value is List && columns != null) {
      final fValues = entity.value as List;
      if (fValues.isEmpty) fValues.add([]);

      int index = 0;
      for (var values in fValues) {
        if (values.isEmpty) {
          final emptyValues = List.generate(
              columns.length,
              (index) => WorkFlowFieldValuesEntity(
                      workspaceId: columns[index].field.workspaceId.toString(),
                      fieldId: columns[index].field.id,
                      ticketId: entity.ticketId,
                      info: columns[index].field,
                      value: null)
                  .toValueJson());
          values.addAll(emptyValues);
        }
        index++;
        List<WorkFlowFieldValuesEntity> fieldValues = [];

        try {
          for (var i = 0; i < columns.length; i++) {
            final formEntity = columns[i].field;
            final listValues = List<Map<String, dynamic>>.from(values);
            final formValue = listValues.firstWhereOrNull(
                (e) => e['id'].toString() == formEntity.id.toString());

            final option = formEntity.option;

            fieldValues.add(
              WorkFlowFieldValuesEntity(
                ticketId: entity.ticketId,
                workspaceId: formEntity.workspaceId.toString(),
                fieldId: formEntity.id,
                value: formValue != null ? formValue['value'] : null,
                // clone object
                info: formEntity.copyWith(
                  option: option.copyWith(
                    formulaArgs: option.formulaArgs?.toList(),
                    currencyPool: option.currencyPool?.toList(),
                    selectionPool: option.selectionPool?.toList(),
                    selections: option.selections?.toList(),
                    columns: option.columns?.toList(),
                  ),
                ),
                isExpanded: isExpanded,
              ),
            );
          }
        } on Exception catch (e, s) {
          GPCoreTracker().appendError(
            'Flutter.ticket.input_table.fromWorkFlowFieldValuesEntity.error',
            data: {
              'error': e.toString(),
              'stacktrace': s.toString(),
            },
          );
          GPCoreTracker().sendLog(
            message: 'Flutter.ticket.input_table.error',
            trackerType: GPTrackerType.ticket,
          );
        }

        rowValues.add(
          TicketTableFieldValues(
            index: index,
            fieldValues: fieldValues,
            isExpanded: isExpanded,
          ),
        );
      }
    }

    return TicketCreateInputTableParams(
      id: formFieldEntity.id,
      formIndex: formIndex,
      formFieldType: formFieldEntity.type,
      title: formFieldEntity.title,
      hint: formFieldEntity.hint,
      subTitle: formFieldEntity.isRequired ? '*' : '',
      isRequired: formFieldEntity.isRequired,
      fieldValues: rowValues,
      isReadOnly: isReadOnly,
      permissions: permissions,
    );
  }

  final String title;
  final String? hint, subTitle;
  final bool isRequired;
  final List<TicketTableFieldValues>? fieldValues;

  @override
  dynamic toTicketValueJson() {
    if (fieldValues == null || fieldValues?.isEmpty == true) {
      return {};
    }

    final ret = [];

    for (var value in fieldValues!) {
      final rowValues = [];
      for (var element in value.fieldValues) {
        rowValues.add(element.toValueJson());
      }

      ret.add(rowValues);
    }

    return ret;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is TicketCreateInputTableParams &&
        other.title == title &&
        other.subTitle == subTitle &&
        other.hint == hint &&
        other.isRequired == isRequired &&
        listEquals(other.fieldValues, fieldValues) &&
        other.id == id &&
        other.formFieldType == formFieldType &&
        other.formIndex == formIndex;
  }

  @override
  int get hashCode {
    return Object.hash(
      title,
      subTitle,
      hint,
      isRequired,
      fieldValues,
      id,
      formFieldType,
      formIndex,
    );
  }
}

final class TicketTableFieldValues {
  TicketTableFieldValues({
    required this.index,
    required this.fieldValues,
    this.isExpanded = true,
  });

  int index;

  final List<WorkFlowFieldValuesEntity> fieldValues;
  bool isExpanded;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is TicketTableFieldValues &&
        other.index == index &&
        listEquals(other.fieldValues, fieldValues) &&
        other.isExpanded == isExpanded;
  }

  @override
  int get hashCode {
    return Object.hash(
      index,
      fieldValues,
      isExpanded,
    );
  }
}

/// TODO: improve performance
/// Hiện tại đang chưa recycle lại được vì parent đang shrinkWrap.
// ignore: must_be_immutable
final class TicketCreateInputTableWidget<P extends TicketCreateInputTableParams>
    extends TicketCreateInputBaseInputWrapperWidget<P> {
  TicketCreateInputTableWidget({
    /// dùng `inputBehavior` ở widget bên ngoài truyền vào
    /// nếu implement inputBehavior sẽ bị sai khi submit form
    required super.inputBehavior,
    required super.params,
    this.onParamsUpdated,
    super.readyOnlyWidget,
    super.key,
    this.editingController,
    this.isCreatingTicket = false,
  }) {
    inputWidget = _TicketCreateInputTableWidget<P>(
      inputBehavior: inputBehavior,
      params: params,
      isCreatingTicket: isCreatingTicket,
    );
  }

  final TextEditingController? editingController;
  final OnParamsUpdated? onParamsUpdated;
  final bool isCreatingTicket;
}

class _TicketCreateInputTableWidget<P extends TicketCreateInputTableParams>
    extends TicketCreateInputBaseInputWidget<P> {
  const _TicketCreateInputTableWidget({
    required super.inputBehavior,
    required super.params,
    super.key,
    required super.isCreatingTicket,
  });

  @override
  State<_TicketCreateInputTableWidget> createState() =>
      _TicketCreateInputTextWidgetState<P>();
}

class _TicketCreateInputTextWidgetState<P extends TicketCreateInputTableParams>
    extends TicketCreateInputBaseInputState<P, _TicketCreateInputTableWidget<P>>
    implements BaseTicketInputBehavior {
  bool canAddRow = true;
  List<int> currentExpandedIndexs = [1];
  @override
  void didUpdateWidget(covariant _TicketCreateInputTableWidget<P> oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.params.isReadOnly || oldWidget.params.id != widget.params.id) {
      params = widget.params;
    }

    for (var element in params.fieldValues ?? <TicketTableFieldValues>[]) {
      element.isExpanded = currentExpandedIndexs.contains(element.index);
    }
  }

  @override
  bool hasData(BaseTicketInputValidateMode mode) {
    // dùng chung form ở parent widget
    // nên không muốn thêm form ở đây nữa.
    return true; //params.isRequired
  }

  @override
  void initState() {
    super.initState();
    hasInitData = _checkTableHasValue();
  }

  bool _checkTableHasValue() {
    bool isHasData = false;
    final row = widget.params.fieldValues?.first;
    for (var fieldValue in row?.fieldValues ?? []) {
      if (fieldValue.value != null && fieldValue.value != '') {
        isHasData = true;
        break;
      }
    }
    return isHasData;
  }

  @override
  bool get canEdit =>
      inputTablePermission?.isFieldCanEdit(
        isCreatingTicket: widget.isCreatingTicket,
        hasValue: hasInitData,
      ) ==
      true;

  WorkflowFormFieldPermissionEntity? get inputTablePermission {
    final isInputTablePermission =
        widget.params.permissions?.isInputPermission ?? false;
    if (isInputTablePermission && hasInitData) {
      return widget.params.permissions?.copyWith(permissionPath: "001");
    }
    return widget.params.permissions;
  }

  @override
  void onSubmit() {
    setState(() {
      {
        for (var element in params.fieldValues ?? <TicketTableFieldValues>[]) {
          element.isExpanded = false;
        }
      }
    });
  }

  @override
  void onSubmitError(Map<int, bool> errors) {
    if (errors.isEmpty) return;
    if (params.fieldValues == null) return;

    final newErros = errors.where((k, v) => v == false);

    setState(() {
      {
        int formIndex = params.formIndex;

        for (var element in params.fieldValues ?? <TicketTableFieldValues>[]) {
          final rowIndex = params.fieldValues!.indexOf(element);

          if (rowIndex > 0) {
            // có 2 rows trở lên, tính lại formIndex dựa trên số lượng items trong row
            formIndex += element.fieldValues.length;
          } else {
            formIndex += rowIndex;
          }

          // tìm index đầu tiên của row chứa lỗi, expand row lỗi đó
          final nextFormIndex = formIndex + element.fieldValues.length;
          final firstIndex = newErros.entries
              .firstWhereOrNull(
                  (e) => e.key >= formIndex && e.key <= nextFormIndex)
              ?.key;

          if (firstIndex != null) {
            element.isExpanded = true;
            currentExpandedIndexs.add(element.index);
            newErros.removeWhere((k, v) => k == firstIndex);
          }
        }
      }
    });
  }

  void onAdd(BuildContext context) {
    if (!canEdit) return;
    if (params.fieldValues == null || params.fieldValues!.isEmpty) {
      return;
    }

    setState(() {
      {
        // thêm 1 row theo row đầu tiên
        final firstItem = params.fieldValues!.first;
        final index = params.fieldValues!.length + 1;

        final fieldValues = firstItem.fieldValues
            .map((e) => WorkFlowFieldValuesEntity.fromJson(e.toJson()))
            .toList();
        if (index == maxTableRows) {
          canAddRow = false;
        } else {
          canAddRow = true;
        }

        for (var element in fieldValues) {
          element.value = null;
          element.info.currentText = '';
        }

        // đóng toàn bộ rows, mở row cuối cùng
        for (var element in params.fieldValues ?? <TicketTableFieldValues>[]) {
          element.isExpanded = false;
        }

        params.fieldValues!.add(
          TicketTableFieldValues(
            index: index,
            fieldValues: fieldValues,
            isExpanded: true,
          ),
        );

        currentExpandedIndexs.clear();
        currentExpandedIndexs.add(params.fieldValues?.length ?? 1);
      }
    });
  }

  void onCopy(BuildContext context, TicketTableFieldValues fieldValues) {
    if (!canEdit) return;
    setState(() {
      {
        if (!canAddRow) return;
        for (var element in params.fieldValues ?? <TicketTableFieldValues>[]) {
          element.isExpanded = false;
        }
        final index = (params.fieldValues?.indexOf(fieldValues) ?? 0) + 1;
        final isExpanded = currentExpandedIndexs.contains(index);
        final newFieldValues = fieldValues.fieldValues
            .map((e) => WorkFlowFieldValuesEntity.fromJson(e.toJson()))
            .toList();
        params.fieldValues!.insert(
          index,
          TicketTableFieldValues(
            index: index,
            fieldValues: newFieldValues,
            isExpanded: isExpanded,
          ),
        );

        for (var i = 0; i < (params.fieldValues?.length ?? 0); i++) {
          params.fieldValues![i].index = i + 1;
        }

        if (index == maxTableRows) {
          canAddRow = false;
        } else {
          canAddRow = true;
        }

        if (isExpanded) {
          currentExpandedIndexs.add(index + 1);
        }

        ticketDetailsScrollController.scrollToIndex(
          index - 3,
          preferPosition: AutoScrollPosition.begin,
        );
      }
    });

    Popup.instance.showSnackBar(
      message: LocaleKeys.ticket_create_input_table_success_duplicate_a_row.tr,
      type: SnackbarType.success,
    );
  }

  void onDelete(BuildContext context, TicketTableFieldValues fieldValues) {
    if (!canEdit) return;
    setState(() {
      {
        final length = params.fieldValues?.length ?? 0;
        canAddRow = true;
        if (length == 1) {
          Popup.instance.showSnackBar(
            message:
                LocaleKeys.ticket_create_input_table_need_atleast_one_row.tr,
            type: SnackbarType.error,
          );
          return;
        }
        final deletedIndex = params.fieldValues?.indexOf(fieldValues);

        if (deletedIndex != null) {
          params.fieldValues?.remove(fieldValues);

          currentExpandedIndexs
              .removeWhere((element) => element == deletedIndex + 1);

          for (var i = deletedIndex; i < length - 1; i++) {
            final item = params.fieldValues![i];
            item.isExpanded = false;
            item.index = i + 1;
          }
        }

        Popup.instance.showSnackBar(
          message: LocaleKeys.ticket_create_input_table_success_remove_a_row.tr,
          type: SnackbarType.success,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    int formIndex = params.formIndex;
    return
        //  AutoScrollTag(
        //   key: ValueKey(params.formIndex),
        //   controller: ticketAutoScrollController,
        //   index: params.formIndex,
        //   child:
        widget.params.permissions?.canView == true
            ? Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
                TitleWidget(
                  title: widget.params.title,
                  subTitle: widget.params.subTitle,
                ),
                const SizedBox(height: 8),
                if (params.fieldValues != null) ...{
                  ...params.fieldValues!.map(
                    (e) {
                      final fieldValues = e;
                      final rowIndex = params.fieldValues!.indexOf(e);

                      if (rowIndex > 0) {
                        // có 2 rows trở lên, tính lại formIndex dựa trên số lượng items trong row
                        formIndex += fieldValues.fieldValues.length;
                      } else {
                        formIndex += rowIndex;
                      }

                      return
                          // AutoScrollTag(
                          //   key: ValueKey(e.index),
                          //   controller: ticketDetailsScrollController,
                          //   index: e.index,
                          //   child:
                          _TicketInputTableRow(
                        index: e.index,
                        formStartIndex: formIndex,
                        inputBehavior: widget.inputBehavior,
                        tableFieldValue: fieldValues,
                        isReadOnly: params.isReadOnly,
                        tablePermission: inputTablePermission,
                        isCreatingTicket: widget.isCreatingTicket,
                        copyCallback: () {
                          onCopy(context, fieldValues);
                        },
                        deleteCallback: () {
                          onDelete(context, fieldValues);
                        },
                        currentExpandedIndexs: currentExpandedIndexs,
                        // ),
                      );
                    },
                  )
                },
                if (!params.isReadOnly && canEdit)
                  canAddRow
                      ? TextButton(
                          onPressed: () => onAdd(context),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SvgWidget(
                                Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC_ADD_SVG,
                                color: GPColor.functionAccentWorkSecondary,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                LocaleKeys
                                    .ticket_create_input_table_button_add_a_row
                                    .tr,
                                style: textStyle(GPTypography.headingSmall)
                                    ?.mergeColor(
                                        GPColor.functionAccentWorkSecondary),
                              ),
                            ],
                          ),
                        )
                      : const SizedBox(),
              ]).paddingSymmetric(horizontal: 16)
            : const SizedBox();
  }
}

class _TicketInputTableRow extends StatelessWidget {
  _TicketInputTableRow({
    required this.index,
    required this.inputBehavior,
    required this.tableFieldValue,
    required this.isReadOnly,
    required this.isCreatingTicket,
    required this.formStartIndex,
    required this.currentExpandedIndexs,
    this.copyCallback,
    this.deleteCallback,
    this.tablePermission,
  });

  // index của row ở trong table
  final int index;

  // start index của row ở trong form, để validate dữ liệu
  final int formStartIndex;

  final bool isReadOnly;
  final bool isCreatingTicket;
  final Function()? copyCallback, deleteCallback;
  final List<int> currentExpandedIndexs;

  late final ExpandableController expandableController =
      ExpandableController(initialExpanded: tableFieldValue.isExpanded);

  late final ValueNotifier<bool> rxExpandable =
      ValueNotifier(tableFieldValue.isExpanded);

  void onCopy(BuildContext context) {
    copyCallback?.call();
  }

  void onDelete(BuildContext context) {
    deleteCallback?.call();
  }

  void onExpand(BuildContext context) {
    expandableController.expanded = !expandableController.expanded;
    rxExpandable.value = expandableController.expanded;

    if (rxExpandable.value) {
      currentExpandedIndexs.add(index);
    } else {
      currentExpandedIndexs.removeWhere((element) => element == index);
    }
  }

  final InputBehavior inputBehavior;
  final TicketTableFieldValues tableFieldValue;
  final WorkflowFormFieldPermissionEntity? tablePermission;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          color: GPColor.bgSecondary,
          padding: const EdgeInsets.symmetric(vertical: 6),
          child: InkWell(
            onTap: () {
              onExpand(context);
            },
            child: ValueListenableBuilder(
              valueListenable: rxExpandable,
              builder: (context, value, child) {
                return Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: 4,
                      height: 24,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: GPColor.functionAccentWorkSecondary,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      '${LocaleKeys.ticket_create_input_table_row.tr} $index',
                      style: textStyle(GPTypography.headingSmall),
                    ),
                    const SizedBox(width: 8),
                    if (expandableController.expanded)
                      SvgWidget(
                        Assets
                            .PACKAGES_GP_ASSETS_IMAGES_SVG_IC16_LINE15_CHEVRON_UP_SVG,
                        color: GPColor.contentTertiary,
                      ),
                    if (!expandableController.expanded)
                      SvgWidget(
                        Assets
                            .PACKAGES_GP_ASSETS_IMAGES_SVG_IC16_LINE15_CHEVRON_DOWN_SVG,
                        color: GPColor.contentTertiary,
                      ),
                    const Spacer(),
                    if (!isReadOnly)
                      TextButton(
                        onPressed: () => onCopy(context),
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.zero,
                          minimumSize: const Size(40, 30),
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                        child: const SvgWidget(
                          Assets
                              .PACKAGES_GP_ASSETS_IMAGES_SVG_IC16_LINE15_2SQUARE_SVG,
                        ),
                      ),
                    if (!isReadOnly)
                      TextButton(
                        onPressed: () => onDelete(context),
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.zero,
                          minimumSize: const Size(40, 30),
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                        child: const SvgWidget(
                          Assets
                              .PACKAGES_GP_ASSETS_IMAGES_SVG_IC16_LINE15_TRASH_SVG,
                        ),
                      ),
                  ],
                );
              },
            ),
          ),
        ),
        ExpandableNotifier(
          controller: expandableController,
          child: Expandable(
            collapsed: const SizedBox(),
            expanded: Container(
              padding: const EdgeInsets.only(top: 12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: GPColor.lineTertiary,
                ),
              ),
              child:
                  //  SingleChildScrollView(
                  //   child:
                  TicketDynamicFormDetailsPage(
                inputBehavior: inputBehavior,
                fields: tableFieldValue.fieldValues,
                isReadOnly: isReadOnly,
                formStartIndex: formStartIndex,
                tablePermission: tablePermission,
                isCreatingTicket: isCreatingTicket,
              ),
              // ),
            ),
          ),
        )
      ],
    );
  }
}
