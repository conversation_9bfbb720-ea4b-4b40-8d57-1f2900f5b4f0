import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:image/image.dart' as img;
import 'package:image_picker/image_picker.dart';

import '../core.dart';
import 'gp_sentry.dart';

Future<Directory> _copyDir() async {
  final documentDirectory = await getApplicationDocumentsDirectory();

  Directory dir = Directory('${documentDirectory.path}/copy');
  if (!await dir.exists()) {
    await dir.create();
  }

  return dir;
}

Future<bool> removeCopyFolder() async {
  try {
    final directory = await _copyDir();
    await directory.delete();
  } catch (e, s) {
    logDebug('removeCopyFolder error: $e, $s');
  }

  return true;
}

class GPPicker with CameraMixin {
  const GPPicker._();

  static const GPPicker instance = GPPicker._();

  static const _sizeHD = 2560;
  static const sizeSD = 1080;
  static const sizeLowSD = 720;

  /// attach files
  /// file type is [FileType.any] or [FileType.media] then to show the file picker
  /// or else type is [FileType.image] need to show the camera preview and take photo
  Future<GPFilePickerResult?> pick(
    FileType fileType, {
    int size = _sizeHD,
    bool allowMultiple = true,
  }) async {
    switch (fileType) {
      case FileType.media:
      case FileType.video:
        return GPPicker.instance.pickMedias();
      case FileType.image:
        if (GetPlatform.isAndroid) {
          final permissions = ["android.permission.CAMERA"];
          var result = await Deeplink.requestPermission(permissions);

          if (result == true) {
            return GPPicker.instance.captureImages(size: size);
          }
        } else {
          return GPPicker.instance.captureImages(size: size);
        }
        break;
      default:
        if (GetPlatform.isAndroid) {
          final permissions = ["android.permission.READ_EXTERNAL_STORAGE"];
          var result = await Deeplink.requestPermission(permissions);

          if (result == true) {
            return GPPicker.instance.pickFiles(allowMultiple: allowMultiple);
          }
        } else {
          return GPPicker.instance.pickFiles(allowMultiple: allowMultiple);
        }
        break;
    }

    return null;
  }

  Future<GPFilePickerResult?> captureImages({
    CameraDevice? cameraDevice,
    required int size,
  }) async {
    final ImagePicker picker = ImagePicker();
    try {
      final XFile? photo = await picker.pickImage(
          source: ImageSource.camera,
          preferredCameraDevice: cameraDevice ?? CameraDevice.rear);
      if (photo != null) {
        final resizePhoto = await resizeImages(photo, size: size);

        return GPFilePickerResult(gpXFiles: [resizePhoto]);
      }
    } catch (e, s) {
      handleCaptureImagesError(e);

      GPCoreTracker().appendError(
        'Flutter:core.captureImages error',
        data: {'ex': e, 'stackTrace': s},
      );
    }

    return null;
  }

  static Future<GPXFile> resizeImages(
    XFile input, {
    required int size,
  }) async {
    final String filePath = input.path;

    try {
      final sizeMap = await _sizeByPath(filePath, size: size);

      final newWidth = sizeMap['width'] ?? 0;
      final newHeight = sizeMap['height'] ?? 0;

      if (newWidth == size || newHeight == size) {
        return GPXFile(
          xFile: input,
          width: newWidth,
          height: newHeight,
        );
      }

      logDebug('resizeImages to [$newWidth, $newHeight]');

      final cmd = img.Command()
        ..decodeImageFile(filePath)
        ..copyResize(width: newWidth, height: newHeight)
        ..writeToFile(filePath);
      await cmd.executeThread();

      return GPXFile(
        xFile: input,
        width: newWidth ?? 0,
        height: newHeight ?? 0,
      );
    } catch (e, s) {
      GPCoreTracker().appendError(
        'Flutter:timeKeeping.resizeImages error',
        data: {'ex': e, 'stackTrace': s},
      );
    }

    return GPXFile(
      xFile: input,
    );
  }

  static Future<Map<String, int>> _sizeByPath(
    String filePath, {
    required int size,
  }) async {
    final ret = <String, int>{};

    File image = File(filePath);
    var decodedImage = await decodeImageFromList(image.readAsBytesSync());
    final width = decodedImage.width;
    final height = decodedImage.height;

    if (kDebugMode) {
      logDebug('DEBUG_TIME_KEEPING image size: $width, $height');
      logDebug('DEBUG_TIME_KEEPING image file size: ${image.lengthSync()}');
    }

    // if (width <= size && height <= size) {
    //   return ret;
    // }

    if (width > height) {
      final ratio = height / width;
      ret.addAll({
        'width': size,
        'height': (size * ratio).toInt(),
      });
    } else if (height > width) {
      final ratio = width / height;
      ret.addAll({
        'height': size,
        'width': (size * ratio).toInt(),
      });
    } else {
      ret.addAll({
        'height': width,
        'width': height,
      });
    }

    return ret;
  }

  Future<GPFilePickerResult?> pickMedias({
    int limit = 16,
  }) async {
    try {
      FilePickerResult? result = await Deeplink.openImagePicker(limit: limit);
      if (result != null) {
        return await _pickerResultHandle(
            GPFilePickerResult(filePickerResult: result));
      } else {
        logDebug('User canceled the picker');
      }
    } catch (e, s) {
      gpCoreLog.severe("Flutter.core.pickMedia.error", e, s);

      FilePickerResult? result = await FilePicker.platform
          .pickFiles(type: FileType.media, allowMultiple: true);
      if (result != null) {
        return await _pickerResultHandle(
            GPFilePickerResult(filePickerResult: result));
        // image picker khi debug ở flutter support copy file tới folder picked_image rồi
        // nhưng chú ý là có giảm quality, không giữ nguyên 100% như flow từ native
        return GPFilePickerResult(filePickerResult: result);
      } else {
        logDebug('User canceled the picker');
      }
      logDebug('$e, $s');
    }

    return null;
  }

  /// copy file to local directory để tránh các folder không thể access ở ios
  /// e.g: /var/mobile/Media/DCIM/101APPLE/IMG_1784.MP4
  Future<GPFilePickerResult?> _pickerResultHandle(
    GPFilePickerResult? result,
  ) async {
    if (result == null) return null;

    final files = result.gpXFiles ??
        result.filePickerResult?.files ??
        result.filePickerResult?.xFiles;

    if (files == null || files.isEmpty) {
      return null;
    }

    if (!Platform.isIOS) {
      return result;
    } else {
      final dir = await _copyDir();
      final platformFiles = <PlatformFile>[];

      Future<File> copyFile(String fileName, File sourceFile) async {
        String newFilePath = '${dir.path}/$fileName';
        final newFile = File(newFilePath);
        await newFile.writeAsBytes(await sourceFile.readAsBytes());
        return newFile;
      }

      for (var file in result.gpXFiles ?? <GPXFile>[]) {
        final cf = await copyFile(
          file.xFile.name,
          File(file.xFile.path),
        );

        file = GPXFile(xFile: XFile(cf.path));

        platformFiles.add(PlatformFile(
            name: file.xFile.name, size: cf.lengthSync(), path: cf.path));
      }

      for (var file in result.filePickerResult?.files ?? <PlatformFile>[]) {
        final cf = await copyFile(
          file.xFile.name,
          File(file.xFile.path),
        );

        file = PlatformFile(
          path: cf.path,
          name: file.xFile.name,
          size: cf.lengthSync(),
        );

        platformFiles.add(file);
      }

      for (var file in result.filePickerResult?.xFiles ?? <XFile>[]) {
        final cf = await copyFile(
          file.name,
          File(file.path),
        );

        file = XFile(cf.path);

        platformFiles.add(PlatformFile(
            name: file.name, size: cf.lengthSync(), path: cf.path));
      }

      return GPFilePickerResult(
        filePickerResult: FilePickerResult(platformFiles.toSet().toList()),
      );
    }
  }

  Future<GPFilePickerResult?> pickFiles({bool allowMultiple = true}) async {
    FilePickerResult? result = await FilePicker.platform
        .pickFiles(type: FileType.any, allowMultiple: allowMultiple);
    if (result != null) {
      return GPFilePickerResult(filePickerResult: result);
    } else {
      logDebug('User canceled the picker');
    }

    return null;
  }
}

final class GPXFile {
  GPXFile({
    required this.xFile,
    this.width = 0,
    this.height = 0,
  });

  final XFile xFile;
  final int width, height;
}

class GPFilePickerResult {
  final List<GPXFile>? gpXFiles;
  final FilePickerResult? filePickerResult;
  GPFilePickerResult({this.gpXFiles, this.filePickerResult});
}

mixin CameraMixin {
  Future handleCaptureImagesError(Object ex) async {
    if (ex is PlatformException) {
      // if (GetPlatform.isIOS) {
      if (ex.code == 'camera_access_denied' ||
          ex.code == 'camera_access_restricted') {
        bool isTheFirstTimeDeniedForever =
            GetStorage().read("cameraPermission") ?? false;

        if (!isTheFirstTimeDeniedForever) {
          GetStorage().write("cameraPermission", true);
          // return null;
        } else {
          return await Get.dialog(DialogOpenSystemSetting(
            title: LocaleKeys.timeKeeping_titleCameraSetting.tr,
            content: LocaleKeys.timeKeeping_contentCameraSetting.tr,
          ));
        }
      }
      // }
    }
  }
}
